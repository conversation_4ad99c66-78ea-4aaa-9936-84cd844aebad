name: Playwright Tests

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build Next.js application
      run: npm run build
    
    - name: Run Playwright tests
      run: npm run test:e2e
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: test-results/
        retention-days: 30

  test-mobile:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build Next.js application
      run: npm run build
    
    - name: Run Mobile Tests
      run: npx playwright test --project="Mobile Chrome" --project="Mobile Safari"
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: mobile-test-results
        path: test-results/
        retention-days: 30

  test-cross-browser:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build Next.js application
      run: npm run build
    
    - name: Run Cross-Browser Tests
      run: npx playwright test --project=chromium --project=firefox --project=webkit
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cross-browser-results
        path: test-results/
        retention-days: 30

  lighthouse:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Build Next.js application
      run: npm run build
    
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli
    
    - name: Run Lighthouse CI
      run: |
        npm run start &
        sleep 10
        lhci autorun --upload.target=temporary-public-storage
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  security-scan:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Run security audit
      run: npm audit --audit-level=high
    
    - name: Check for vulnerabilities
      run: npx audit-ci --config audit-ci.json
      continue-on-error: true
