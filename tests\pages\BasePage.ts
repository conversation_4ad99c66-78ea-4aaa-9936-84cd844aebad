import { Page, Locator, expect } from '@playwright/test';

export class BasePage {
  readonly page: Page;
  readonly navigation: Locator;
  readonly mobileMenuButton: Locator;
  readonly mobileMenu: Locator;

  constructor(page: Page) {
    this.page = page;
    this.navigation = page.locator('nav');
    this.mobileMenuButton = page.locator('[data-testid="mobile-menu-button"]');
    this.mobileMenu = page.locator('[data-testid="mobile-menu"]');
  }

  async goto(path: string = '/') {
    await this.page.goto(path);
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    // Wait for the page to be fully loaded
    await this.page.waitForLoadState('networkidle');
    
    // Wait for any animations to complete
    await this.page.waitForTimeout(1000);
  }

  async getTitle() {
    return await this.page.title();
  }

  async getCurrentUrl() {
    return this.page.url();
  }

  // Navigation helpers
  async clickNavLink(linkText: string) {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      await this.openMobileMenu();
      await this.page.getByRole('link', { name: linkText }).click();
    } else {
      await this.page.getByRole('link', { name: linkText }).click();
    }
    
    await this.waitForPageLoad();
  }

  async openMobileMenu() {
    const isMobile = await this.isMobileViewport();
    if (isMobile) {
      // Look for hamburger menu button
      const menuButton = this.page.locator('button').filter({ hasText: /menu|hamburger/i }).first();
      if (await menuButton.isVisible()) {
        await menuButton.click();
        await this.page.waitForTimeout(500); // Wait for menu animation
      }
    }
  }

  async isMobileViewport() {
    const viewport = this.page.viewportSize();
    return viewport ? viewport.width < 768 : false;
  }

  // Common assertions
  async expectPageTitle(expectedTitle: string | RegExp) {
    await expect(this.page).toHaveTitle(expectedTitle);
  }

  async expectUrl(expectedUrl: string | RegExp) {
    await expect(this.page).toHaveURL(expectedUrl);
  }

  async expectElementVisible(selector: string) {
    await expect(this.page.locator(selector)).toBeVisible();
  }

  async expectElementHidden(selector: string) {
    await expect(this.page.locator(selector)).toBeHidden();
  }

  // Error handling
  async checkForConsoleErrors() {
    const logs: string[] = [];
    
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        // Filter out expected WebGL errors that occur in headless environments
        if (!text.includes('WebGL') && 
            !text.includes('THREE.WebGLRenderer') && 
            !text.includes('canvas.getContext') &&
            !text.includes('WebGL context')) {
          logs.push(text);
        }
      }
    });

    // Wait a bit to catch any console errors
    await this.page.waitForTimeout(2000);
    
    return logs;
  }

  // Screenshot helper
  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}.png`,
      fullPage: true 
    });
  }
}
