import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class ContactPage extends BasePage {
  readonly pageTitle: Locator;
  readonly contactForm: Locator;
  readonly nameInput: Locator;
  readonly emailInput: Locator;
  readonly messageInput: Locator;
  readonly submitButton: Locator;
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.getByRole('heading', { name: /contact/i });
    this.contactForm = page.locator('form');
    this.nameInput = page.locator('input[name="name"]');
    this.emailInput = page.locator('input[name="email"]');
    this.messageInput = page.locator('textarea[name="message"]');
    this.submitButton = page.getByRole('button', { name: /send|submit/i });
    this.successMessage = page.locator('[data-testid="success-message"]');
    this.errorMessage = page.locator('[data-testid="error-message"]');
  }

  async goto() {
    await super.goto('/contact');
  }

  async expectPageTitle() {
    await expect(this.pageTitle).toBeVisible();
  }

  async expectContactForm() {
    await expect(this.contactForm).toBeVisible();
    await expect(this.nameInput).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.messageInput).toBeVisible();
    await expect(this.submitButton).toBeVisible();
  }

  async fillContactForm(name: string, email: string, message: string) {
    await this.nameInput.fill(name);
    await this.emailInput.fill(email);
    await this.messageInput.fill(message);
  }

  async submitForm() {
    await this.submitButton.click();
    await this.page.waitForTimeout(1000); // Wait for form submission
  }

  async expectSuccessMessage() {
    await expect(this.successMessage).toBeVisible();
  }

  async expectErrorMessage() {
    await expect(this.errorMessage).toBeVisible();
  }

  async testFormValidation() {
    // Test empty form submission
    await this.submitForm();
    
    // Should show validation errors
    await expect(this.page.getByText(/required/i)).toBeVisible();
  }

  async testInvalidEmail() {
    await this.fillContactForm('Test User', 'invalid-email', 'Test message');
    await this.submitForm();
    
    // Should show email validation error
    await expect(this.page.getByText(/valid email/i)).toBeVisible();
  }

  async testValidFormSubmission() {
    await this.fillContactForm(
      'Test User',
      '<EMAIL>',
      'This is a test message for the contact form.'
    );
    
    await this.submitForm();
    
    // Should show success message or redirect
    // Note: This depends on your actual form implementation
  }

  async expectContactInformation() {
    // Check for contact information display
    await expect(this.page.getByText(/email/i)).toBeVisible();
    await expect(this.page.getByText(/phone/i)).toBeVisible();
  }

  async expectSocialLinks() {
    // Check for social media links if they exist
    const socialLinks = this.page.locator('a[href*="instagram"], a[href*="twitter"], a[href*="facebook"]');
    const count = await socialLinks.count();
    
    if (count > 0) {
      await expect(socialLinks.first()).toBeVisible();
    }
  }

  async expectResponsiveLayout() {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      // On mobile, form should be full width
      await this.expectContactForm();
    } else {
      // On desktop, form might be in a specific layout
      await this.expectContactForm();
    }
  }
}
