# Test info

- Name: Noize Capital - Working Demo Tests >> navigation to services page works
- Location: C:\Users\<USER>\Coding Projects\noize-capital\tests\demo-working.spec.ts:22:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)

Locator: locator(':root')
Expected pattern: /\/services/
Received string:  "http://localhost:3000/"
Call log:
  - expect.toHaveURL with timeout 5000ms
  - waiting for locator(':root')
    7 × locator resolved to <html lang="en" class="dark">…</html>
      - unexpected value "http://localhost:3000/"

    at C:\Users\<USER>\Coding Projects\noize-capital\tests\demo-working.spec.ts:30:24
```

# Page snapshot

```yaml
- link "Noize Capital":
  - /url: /
- navigation "Main":
  - list:
    - listitem:
      - link "Home":
        - /url: /
    - listitem:
      - link "Services":
        - /url: /services
    - listitem:
      - link "Portfolio":
        - /url: /portfolio
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "About":
        - /url: /about
    - listitem:
      - link "Contact":
        - /url: /contact
- link "Book Now":
  - /url: /contact
- main:
  - img "Noize Capital"
  - heading "Noize Capital P r e c i s i o n S o u n d . I n d e p e n d e n t S p i r i t ." [level=1]
  - paragraph: Where we make your ideas audible
  - link "Our Services":
    - /url: /services
- region "Notifications alt+T"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Noize Capital - Working Demo Tests', () => {
   4 |   test('homepage loads and displays key elements', async ({ page }) => {
   5 |     await page.goto('http://localhost:3000');
   6 |     
   7 |     // Check title
   8 |     await expect(page).toHaveTitle(/Noize Capital/);
   9 |     
   10 |     // Check main logo is visible
   11 |     await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
   12 |     
   13 |     // Check tagline
   14 |     await expect(page.getByText('Where we make your ideas audible')).toBeVisible();
   15 |     
   16 |     // Check services button
   17 |     await expect(page.getByRole('link', { name: 'Our Services' })).toBeVisible();
   18 |     
   19 |     console.log('✅ Homepage test passed!');
   20 |   });
   21 |
   22 |   test('navigation to services page works', async ({ page }) => {
   23 |     await page.goto('http://localhost:3000');
   24 |     
   25 |     // Click on Services in navigation
   26 |     await page.getByRole('link', { name: 'Services' }).first().click();
   27 |     await page.waitForLoadState('networkidle');
   28 |     
   29 |     // Check we're on services page
>  30 |     await expect(page).toHaveURL(/\/services/);
      |                        ^ Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)
   31 |     
   32 |     // Check for pricing information
   33 |     await expect(page.getByText('$35')).toBeVisible();
   34 |     await expect(page.getByText('$20')).toBeVisible();
   35 |     await expect(page.getByText('$50')).toBeVisible();
   36 |     
   37 |     console.log('✅ Services navigation test passed!');
   38 |   });
   39 |
   40 |   test('blog page displays content', async ({ page }) => {
   41 |     await page.goto('http://localhost:3000/blog');
   42 |     
   43 |     // Check page loads
   44 |     await expect(page.getByText('Insights, tutorials, and guides')).toBeVisible();
   45 |     
   46 |     // Check for specific blog post (using partial text to avoid strict mode)
   47 |     await expect(page.getByText('AI Mastering vs $500 Professional Mastering')).toBeVisible();
   48 |     
   49 |     console.log('✅ Blog page test passed!');
   50 |   });
   51 |
   52 |   test('portfolio page displays projects', async ({ page }) => {
   53 |     await page.goto('http://localhost:3000/portfolio');
   54 |     
   55 |     // Check page title
   56 |     await expect(page.getByText('Our Portfolio')).toBeVisible();
   57 |     
   58 |     // Check for specific project
   59 |     await expect(page.getByText('Amadoda')).toBeVisible();
   60 |     
   61 |     // Check for filter button (using role to be more specific)
   62 |     await expect(page.getByRole('button', { name: 'All' })).toBeVisible();
   63 |     
   64 |     console.log('✅ Portfolio page test passed!');
   65 |   });
   66 |
   67 |   test('about page displays team information', async ({ page }) => {
   68 |     await page.goto('http://localhost:3000/about');
   69 |     
   70 |     // Check page title
   71 |     await expect(page.getByText('About Us')).toBeVisible();
   72 |     
   73 |     // Check for company description
   74 |     await expect(page.getByText('Passionate about sound, dedicated to your music')).toBeVisible();
   75 |     
   76 |     // Check for founder info (using more specific selector)
   77 |     await expect(page.getByText('Founder & Lead Engineer')).toBeVisible();
   78 |     
   79 |     console.log('✅ About page test passed!');
   80 |   });
   81 |
   82 |   test('responsive design - mobile viewport', async ({ page }) => {
   83 |     // Set mobile viewport
   84 |     await page.setViewportSize({ width: 375, height: 667 });
   85 |     
   86 |     await page.goto('http://localhost:3000');
   87 |     
   88 |     // Check that main content is still visible on mobile
   89 |     await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
   90 |     await expect(page.getByText('Where we make your ideas audible')).toBeVisible();
   91 |     
   92 |     console.log('✅ Mobile responsive test passed!');
   93 |   });
   94 |
   95 |   test('no critical console errors on homepage', async ({ page }) => {
   96 |     const errors: string[] = [];
   97 |     
   98 |     page.on('console', msg => {
   99 |       if (msg.type() === 'error') {
  100 |         const text = msg.text();
  101 |         // Filter out expected WebGL errors in headless mode
  102 |         if (!text.includes('WebGL') && 
  103 |             !text.includes('THREE.WebGLRenderer') && 
  104 |             !text.includes('canvas.getContext') &&
  105 |             !text.includes('WebGL context')) {
  106 |           errors.push(text);
  107 |         }
  108 |       }
  109 |     });
  110 |
  111 |     await page.goto('http://localhost:3000');
  112 |     await page.waitForTimeout(3000); // Wait for any async operations
  113 |     
  114 |     // Should have no critical errors
  115 |     expect(errors).toHaveLength(0);
  116 |     
  117 |     console.log('✅ Console errors test passed!');
  118 |   });
  119 |
  120 |   test('page performance is reasonable', async ({ page }) => {
  121 |     const startTime = Date.now();
  122 |     
  123 |     await page.goto('http://localhost:3000');
  124 |     await page.waitForLoadState('networkidle');
  125 |     
  126 |     const loadTime = Date.now() - startTime;
  127 |     
  128 |     // Should load within 15 seconds (generous for CI/slow networks)
  129 |     expect(loadTime).toBeLessThan(15000);
  130 |     
```