import { test, expect } from '@playwright/test';
import { HomePage } from './pages/HomePage';
import { ServicesPage } from './pages/ServicesPage';
import { PortfolioPage } from './pages/PortfolioPage';
import { BlogPage } from './pages/BlogPage';
import { ContactPage } from './pages/ContactPage';
import { TestHelpers } from './utils/test-helpers';

test.describe('End-to-End User Flows', () => {
  test('complete user journey: homepage to contact', async ({ page }) => {
    // Start at homepage
    const homePage = new HomePage(page);
    await homePage.goto();
    await homePage.expectPageContent();

    // Navigate to services
    await homePage.clickServicesButton();
    const servicesPage = new ServicesPage(page);
    await servicesPage.expectPricingCards();

    // Navigate to portfolio
    await servicesPage.clickNavLink('Portfolio');
    const portfolioPage = new PortfolioPage(page);
    await portfolioPage.expectProjectCards();

    // Navigate to blog
    await portfolioPage.clickNavLink('Blog');
    const blogPage = new BlogPage(page);
    await blogPage.expectBlogPosts();

    // Navigate to contact
    await blogPage.clickNavLink('Contact');
    const contactPage = new ContactPage(page);
    await contactPage.expectContactForm();

    // Fill and submit contact form
    const testData = TestHelpers.generateTestData();
    await contactPage.fillContactForm(
      testData.user.name,
      testData.user.email,
      testData.user.message
    );
  });

  test('portfolio filtering and audio interaction flow', async ({ page }) => {
    const portfolioPage = new PortfolioPage(page);
    await portfolioPage.goto();

    // Test complete filter workflow
    await portfolioPage.testFilterFunctionality();

    // Test audio players
    await portfolioPage.expectAudioPlayers();
    await portfolioPage.expectSpotifyEmbeds();

    // Navigate to contact from portfolio CTA
    await portfolioPage.expectCallToAction();
    const ctaButton = page.getByRole('link', { name: 'Get Started' });
    if (await ctaButton.isVisible()) {
      await ctaButton.click();
      await TestHelpers.waitForNetworkIdle(page);
    }
  });

  test('blog reading experience flow', async ({ page }) => {
    const blogPage = new BlogPage(page);
    await blogPage.goto();

    // Filter by tag
    await blogPage.filterByTag('mixing');
    await page.waitForTimeout(500);

    // Read a blog post
    await blogPage.clickBlogPost('Getting Started with Mixing');
    
    // Test blog post features
    const blogPostPage = new BlogPage(page);
    await expect(page.locator('h1')).toBeVisible();
    
    // Navigate back to blog
    const backLink = page.getByRole('link', { name: /back to blog/i });
    if (await backLink.isVisible()) {
      await backLink.click();
      await TestHelpers.waitForNetworkIdle(page);
    }
  });

  test('services to contact conversion flow', async ({ page }) => {
    const servicesPage = new ServicesPage(page);
    await servicesPage.goto();

    // Explore services
    await servicesPage.expectPricingCards();
    await servicesPage.testFAQInteractivity();

    // Navigate to contact
    await servicesPage.clickNavLink('Contact');
    const contactPage = new ContactPage(page);
    await contactPage.expectContactForm();

    // Fill form with service inquiry
    await contactPage.fillContactForm(
      'Potential Client',
      '<EMAIL>',
      'I am interested in your mastering services. Can you provide more information about your process?'
    );
  });

  test('mobile user experience flow', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Test mobile navigation across pages
    const homePage = new HomePage(page);
    await homePage.goto();
    await homePage.expectResponsiveLayout();

    // Test mobile menu navigation
    await homePage.openMobileMenu();
    await homePage.clickNavLink('Services');

    const servicesPage = new ServicesPage(page);
    await servicesPage.expectResponsiveLayout();

    await servicesPage.openMobileMenu();
    await servicesPage.clickNavLink('Portfolio');

    const portfolioPage = new PortfolioPage(page);
    await portfolioPage.expectResponsiveGrid();
  });

  test('accessibility compliance across pages', async ({ page }) => {
    const pages = [
      { path: '/', name: 'Homepage' },
      { path: '/services', name: 'Services' },
      { path: '/portfolio', name: 'Portfolio' },
      { path: '/blog', name: 'Blog' },
      { path: '/contact', name: 'Contact' },
      { path: '/about', name: 'About' },
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.path}`);
      await TestHelpers.waitForNetworkIdle(page);
      
      // Check accessibility for each page
      await TestHelpers.checkAccessibility(page);
      
      console.log(`✅ Accessibility check passed for ${pageInfo.name}`);
    }
  });

  test('performance across all pages', async ({ page }) => {
    const pages = [
      { path: '/', name: 'Homepage' },
      { path: '/services', name: 'Services' },
      { path: '/portfolio', name: 'Portfolio' },
      { path: '/blog', name: 'Blog' },
      { path: '/contact', name: 'Contact' },
      { path: '/about', name: 'About' },
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.path}`);
      await TestHelpers.waitForNetworkIdle(page);
      
      // Check performance for each page
      await TestHelpers.checkPerformance(page);
      
      console.log(`✅ Performance check passed for ${pageInfo.name}`);
    }
  });

  test('SEO compliance across pages', async ({ page }) => {
    const pages = [
      { path: '/', name: 'Homepage' },
      { path: '/services', name: 'Services' },
      { path: '/portfolio', name: 'Portfolio' },
      { path: '/blog', name: 'Blog' },
      { path: '/contact', name: 'Contact' },
      { path: '/about', name: 'About' },
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.path}`);
      await TestHelpers.waitForNetworkIdle(page);
      
      // Check SEO for each page
      await TestHelpers.checkSEO(page);
      
      console.log(`✅ SEO check passed for ${pageInfo.name}`);
    }
  });

  test('error handling and resilience', async ({ page }) => {
    // Test 404 page
    await page.goto('http://localhost:3000/non-existent-page');
    
    // Should show 404 page or redirect
    const is404 = page.url().includes('404') || 
                  await page.locator('text=404').isVisible() ||
                  await page.locator('text=Not Found').isVisible();
    
    if (!is404) {
      // If no 404 page, should at least not crash
      await expect(page.locator('body')).toBeVisible();
    }

    // Test navigation recovery
    const homePage = new HomePage(page);
    await homePage.goto();
    await homePage.expectPageContent();
  });
});
