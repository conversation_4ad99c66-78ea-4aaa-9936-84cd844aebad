import { test, expect } from '@playwright/test';
import { BlogPage, BlogPostPage } from './pages/BlogPage';
import { TestHelpers } from './utils/test-helpers';

test.describe('Blog Page', () => {
  let blogPage: BlogPage;

  test.beforeEach(async ({ page }) => {
    blogPage = new BlogPage(page);
    await blogPage.goto();
  });

  test('should display page title', async () => {
    await blogPage.expectPageTitle();
  });

  test('should display blog posts', async () => {
    await blogPage.expectBlogPosts();
  });

  test('should display post metadata', async () => {
    await blogPage.expectPostMetadata();
  });

  test('should have tags filter', async () => {
    await blogPage.expectTagsFilter();
  });

  test('should filter posts by tag', async () => {
    await blogPage.expectFilteredPosts('mixing');
  });

  test('should be responsive', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await blogPage.expectResponsiveLayout();
  });

  test('should load without console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page);
    expect(errors).toHaveLength(0);
  });

  test('should navigate to individual blog posts', async ({ page }) => {
    await blogPage.clickBlogPost('Getting Started with Mixing');
    await expect(page).toHaveURL(/\/blog\/.+/);
  });
});

test.describe('Blog Post Page', () => {
  let blogPostPage: BlogPostPage;

  test.beforeEach(async ({ page }) => {
    blogPostPage = new BlogPostPage(page);
    await blogPostPage.goto('getting-started-with-mixing');
  });

  test('should display post title', async () => {
    await blogPostPage.expectPostTitle();
  });

  test('should display post content', async () => {
    await blogPostPage.expectPostContent();
  });

  test('should display table of contents on desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    await blogPostPage.expectTableOfContents();
  });

  test('should display reading progress', async () => {
    await blogPostPage.expectReadingProgress();
  });

  test('should have working table of contents navigation', async () => {
    await blogPostPage.testTableOfContentsNavigation();
  });

  test('should update reading progress on scroll', async () => {
    await blogPostPage.testReadingProgress();
  });

  test('should display post metadata', async () => {
    await blogPostPage.expectPostMetadata();
  });

  test('should have back to blog link', async () => {
    await blogPostPage.expectBackToBlogLink();
  });

  test('should navigate back to blog', async ({ page }) => {
    await blogPostPage.clickBackToBlog();
    await expect(page).toHaveURL(/\/blog$/);
  });

  test('should load without console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page);
    expect(errors).toHaveLength(0);
  });
});
