import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class BlogPage extends BasePage {
  readonly pageTitle: Locator;
  readonly postCards: Locator;
  readonly tagsFilter: Locator;
  readonly searchInput: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.getByRole('heading', { name: /blog/i });
    this.postCards = page.locator('[data-testid="post-card"]');
    this.tagsFilter = page.locator('[data-testid="tags-filter"]');
    this.searchInput = page.locator('input[type="search"]');
  }

  async goto() {
    await super.goto('/blog');
  }

  async expectPageTitle() {
    await expect(this.pageTitle).toBeVisible();
  }

  async expectBlogPosts() {
    // Check for specific blog posts
    await expect(this.page.getByText('Getting Started with Mixing')).toBeVisible();
    await expect(this.page.getByText('How to Prepare Your Track for Mixing')).toBeVisible();
    await expect(this.page.getByText('Why Mastering Still Matters in 2025')).toBeVisible();
  }

  async expectPostMetadata() {
    // Check for post metadata like dates, tags, etc.
    await expect(this.page.getByText('mixing')).toBeVisible();
    await expect(this.page.getByText('mastering')).toBeVisible();
    await expect(this.page.getByText('production')).toBeVisible();
  }

  async clickBlogPost(postTitle: string) {
    await this.page.getByRole('link', { name: postTitle }).click();
    await this.waitForPageLoad();
  }

  async expectTagsFilter() {
    // Check for tag filtering functionality
    await expect(this.page.getByText('All')).toBeVisible();
    await expect(this.page.getByText('mixing')).toBeVisible();
    await expect(this.page.getByText('mastering')).toBeVisible();
  }

  async filterByTag(tag: string) {
    await this.page.getByRole('button', { name: tag }).click();
    await this.page.waitForTimeout(500);
  }

  async expectFilteredPosts(tag: string) {
    // After filtering, check that only posts with the selected tag are shown
    await this.filterByTag(tag);
    
    // The filtered posts should contain the tag
    const posts = this.page.locator('[data-testid="post-card"]');
    const count = await posts.count();
    expect(count).toBeGreaterThan(0);
  }

  async expectResponsiveLayout() {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      // On mobile, posts should stack vertically
      await this.expectBlogPosts();
    } else {
      // On desktop, posts might be in a grid
      await this.expectBlogPosts();
    }
  }
}

export class BlogPostPage extends BasePage {
  readonly postTitle: Locator;
  readonly postContent: Locator;
  readonly tableOfContents: Locator;
  readonly readingProgress: Locator;
  readonly backToBlog: Locator;

  constructor(page: Page) {
    super(page);
    this.postTitle = page.locator('h1');
    this.postContent = page.locator('[data-testid="post-content"]');
    this.tableOfContents = page.locator('[data-testid="table-of-contents"]');
    this.readingProgress = page.locator('[data-testid="reading-progress"]');
    this.backToBlog = page.getByRole('link', { name: /back to blog/i });
  }

  async goto(slug: string) {
    await super.goto(`/blog/${slug}`);
  }

  async expectPostTitle() {
    await expect(this.postTitle).toBeVisible();
  }

  async expectPostContent() {
    await expect(this.postContent).toBeVisible();
  }

  async expectTableOfContents() {
    // Check if TOC is visible (should be on the side for desktop)
    const isMobile = await this.isMobileViewport();
    
    if (!isMobile) {
      await expect(this.tableOfContents).toBeVisible();
    }
  }

  async expectReadingProgress() {
    // Check for reading progress indicator
    await expect(this.readingProgress).toBeVisible();
  }

  async testTableOfContentsNavigation() {
    // Test clicking on TOC items
    const tocLinks = this.page.locator('[data-testid="toc-link"]');
    const count = await tocLinks.count();
    
    if (count > 0) {
      await tocLinks.first().click();
      await this.page.waitForTimeout(500);
      // The page should scroll to the corresponding section
    }
  }

  async testReadingProgress() {
    // Scroll down and check if reading progress updates
    await this.page.evaluate(() => window.scrollTo(0, document.body.scrollHeight / 2));
    await this.page.waitForTimeout(500);
    
    // Check if progress indicator has updated
    await expect(this.readingProgress).toBeVisible();
  }

  async expectPostMetadata() {
    // Check for post metadata
    await expect(this.page.locator('[data-testid="post-date"]')).toBeVisible();
    await expect(this.page.locator('[data-testid="post-tags"]')).toBeVisible();
  }

  async expectBackToBlogLink() {
    await expect(this.backToBlog).toBeVisible();
  }

  async clickBackToBlog() {
    await this.backToBlog.click();
    await this.waitForPageLoad();
  }
}
