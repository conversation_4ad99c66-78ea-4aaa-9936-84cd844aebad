name: CI/CD Pipeline

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

env:
  NODE_VERSION: 'lts/*'

jobs:
  # Quality checks
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Run ESLint
      run: npm run lint
    
    - name: Type check
      run: npx tsc --noEmit
    
    - name: Build application
      run: npm run build

  # E2E Testing
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: quality
    
    strategy:
      fail-fast: false
      matrix:
        project: [chromium, firefox, webkit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps ${{ matrix.project }}
    
    - name: Build application
      run: npm run build
    
    - name: Run Playwright tests
      run: npx playwright test --project=${{ matrix.project }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-results-${{ matrix.project }}
        path: |
          playwright-report/
          test-results/
        retention-days: 30

  # Mobile Testing
  mobile-tests:
    name: Mobile Tests
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium
    
    - name: Build application
      run: npm run build
    
    - name: Run mobile tests
      run: npx playwright test --project="Mobile Chrome" --project="Mobile Safari"
    
    - name: Upload mobile test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: mobile-test-results
        path: |
          playwright-report/
          test-results/
        retention-days: 30

  # Performance Testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Build application
      run: npm run build
    
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli
    
    - name: Run Lighthouse CI
      run: lhci autorun
    
    - name: Upload Lighthouse results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: lighthouse-results
        path: .lighthouseci/
        retention-days: 30

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Run security audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
    
    - name: Install audit-ci
      run: npm install -g audit-ci
    
    - name: Run audit-ci
      run: audit-ci --config audit-ci.json
      continue-on-error: true

  # Test Summary
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [e2e-tests, mobile-tests, performance, security]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
    
    - name: Create test summary
      run: |
        echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### E2E Tests" >> $GITHUB_STEP_SUMMARY
        echo "- Chromium: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Firefox: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- WebKit: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Mobile Tests" >> $GITHUB_STEP_SUMMARY
        echo "- Status: ${{ needs.mobile-tests.result }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Performance" >> $GITHUB_STEP_SUMMARY
        echo "- Lighthouse: ${{ needs.performance.result }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Security" >> $GITHUB_STEP_SUMMARY
        echo "- Audit: ${{ needs.security.result }}" >> $GITHUB_STEP_SUMMARY

  # Deployment (only on main branch)
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality, e2e-tests, mobile-tests, performance, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
