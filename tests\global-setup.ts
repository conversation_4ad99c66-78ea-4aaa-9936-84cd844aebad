import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for Noize Capital e2e tests...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for the dev server to be ready
    console.log('⏳ Waiting for dev server to be ready...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('✅ Dev server is ready!');
    
    // Perform any global setup tasks here
    // For example, you could:
    // - Clear any existing data
    // - Set up test data
    // - Authenticate users
    // - Check if required services are running
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('✅ Global setup completed successfully!');
}

export default globalSetup;
