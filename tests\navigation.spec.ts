import { test, expect } from '@playwright/test';
import { BasePage } from './pages/BasePage';
import { TestHelpers } from './utils/test-helpers';

test.describe('Navigation', () => {
  let basePage: BasePage;

  test.beforeEach(async ({ page }) => {
    basePage = new BasePage(page);
    await basePage.goto();
  });

  test('should navigate to all main pages from homepage', async ({ page }) => {
    const navItems = [
      { name: 'Services', url: '/services' },
      { name: 'Portfolio', url: '/portfolio' },
      { name: 'Blog', url: '/blog' },
      { name: 'About', url: '/about' },
      { name: 'Contact', url: '/contact' },
    ];

    for (const item of navItems) {
      await basePage.goto('/'); // Start from homepage
      await basePage.clickNavLink(item.name);
      await expect(page).toHaveURL(new RegExp(item.url));
    }
  });

  test('should navigate back to homepage from logo', async ({ page }) => {
    // Go to a different page first
    await basePage.clickNavLink('Services');
    await expect(page).toHaveURL(/\/services/);
    
    // Click on logo/brand name to go back to homepage
    await page.getByRole('link', { name: 'Noize Capital' }).click();
    await basePage.waitForPageLoad();
    await expect(page).toHaveURL(/^.*\/$|^.*\/$/); // Homepage URL
  });

  test('should work on mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Test mobile navigation
    await basePage.openMobileMenu();
    await basePage.clickNavLink('Services');
    await expect(page).toHaveURL(/\/services/);
  });

  test('should highlight current page in navigation', async ({ page }) => {
    // Go to services page
    await basePage.clickNavLink('Services');
    
    // Check if services nav item is highlighted
    const servicesLink = page.getByRole('link', { name: 'Services' });
    const classes = await servicesLink.getAttribute('class');
    expect(classes).toContain('text-orange-500'); // Active state class
  });

  test('should maintain navigation state across page transitions', async ({ page }) => {
    // Navigate through multiple pages
    await basePage.clickNavLink('Services');
    await expect(page).toHaveURL(/\/services/);
    
    await basePage.clickNavLink('Portfolio');
    await expect(page).toHaveURL(/\/portfolio/);
    
    await basePage.clickNavLink('Blog');
    await expect(page).toHaveURL(/\/blog/);
    
    // Navigation should still be visible and functional
    await expect(basePage.navigation).toBeVisible();
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Test Tab navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Should be able to navigate with Enter key
    const focusedElement = page.locator(':focus');
    await focusedElement.press('Enter');
    
    // Should navigate to the focused link
    await basePage.waitForPageLoad();
  });

  test('should work with browser back/forward buttons', async ({ page }) => {
    // Navigate to services
    await basePage.clickNavLink('Services');
    await expect(page).toHaveURL(/\/services/);
    
    // Navigate to portfolio
    await basePage.clickNavLink('Portfolio');
    await expect(page).toHaveURL(/\/portfolio/);
    
    // Use browser back button
    await page.goBack();
    await expect(page).toHaveURL(/\/services/);
    
    // Use browser forward button
    await page.goForward();
    await expect(page).toHaveURL(/\/portfolio/);
  });

  test('should handle direct URL navigation', async ({ page }) => {
    // Test direct navigation to each page
    const pages = ['/services', '/portfolio', '/blog', '/about', '/contact'];
    
    for (const pagePath of pages) {
      await page.goto(`http://localhost:3000${pagePath}`);
      await basePage.waitForPageLoad();
      await expect(page).toHaveURL(new RegExp(pagePath));
      
      // Navigation should still be visible
      await expect(basePage.navigation).toBeVisible();
    }
  });

  test('should maintain responsive behavior during navigation', async ({ page }) => {
    // Test navigation on different screen sizes
    const viewports = [
      { width: 375, height: 667 }, // Mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1920, height: 1080 }, // Desktop
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await basePage.goto('/');
      
      // Navigation should be accessible
      if (viewport.width < 768) {
        // Mobile: check for hamburger menu
        await basePage.openMobileMenu();
      }
      
      await basePage.clickNavLink('Services');
      await expect(page).toHaveURL(/\/services/);
    }
  });

  test('should handle navigation animations smoothly', async ({ page }) => {
    // Test that page transitions work smoothly
    await basePage.clickNavLink('Services');
    await TestHelpers.waitForAnimations(page);
    await expect(page).toHaveURL(/\/services/);
    
    await basePage.clickNavLink('Portfolio');
    await TestHelpers.waitForAnimations(page);
    await expect(page).toHaveURL(/\/portfolio/);
  });

  test('should not break on rapid navigation clicks', async ({ page }) => {
    // Rapidly click different navigation items
    await Promise.all([
      basePage.clickNavLink('Services'),
      page.waitForTimeout(100),
      basePage.clickNavLink('Portfolio'),
      page.waitForTimeout(100),
      basePage.clickNavLink('Blog'),
    ]);
    
    // Should end up on the last clicked page
    await basePage.waitForPageLoad();
    await expect(page).toHaveURL(/\/blog/);
  });
});
