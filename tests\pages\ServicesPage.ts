import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class ServicesPage extends BasePage {
  readonly pageTitle: Locator;
  readonly pricingCards: Locator;
  readonly mixingCard: Locator;
  readonly masteringCard: Locator;
  readonly fullPackageCard: Locator;
  readonly faqSection: Locator;
  readonly faqItems: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.getByRole('heading', { name: /services/i });
    this.pricingCards = page.locator('[class*="card"]');
    this.mixingCard = page.getByText('Mixing').locator('..');
    this.masteringCard = page.getByText('Mastering').locator('..');
    this.fullPackageCard = page.getByText('Full Package').locator('..');
    this.faqSection = page.locator('[class*="faq"]');
    this.faqItems = page.locator('[data-testid="faq-item"]');
  }

  async goto() {
    await super.goto('/services');
  }

  async expectPageTitle() {
    await expect(this.pageTitle).toBeVisible();
  }

  async expectPricingCards() {
    // Check that all three service cards are visible
    await expect(this.page.getByText('Mixing')).toBeVisible();
    await expect(this.page.getByText('Mastering')).toBeVisible();
    await expect(this.page.getByText('Full Package')).toBeVisible();
  }

  async expectPricingInformation() {
    // Check for pricing information
    await expect(this.page.getByText('$35')).toBeVisible(); // Mixing price
    await expect(this.page.getByText('$20')).toBeVisible(); // Mastering price
    await expect(this.page.getByText('$50')).toBeVisible(); // Full package price
  }

  async expectServiceFeatures() {
    // Check for service features
    await expect(this.page.getByText('Up to 8 tracks')).toBeVisible();
    await expect(this.page.getByText('2 revisions')).toBeVisible();
    await expect(this.page.getByText('Stereo track mastering')).toBeVisible();
    await expect(this.page.getByText('3 revisions')).toBeVisible();
  }

  async expectFAQSection() {
    await expect(this.page.getByText('What formats do you accept?')).toBeVisible();
    await expect(this.page.getByText('How many revisions are included?')).toBeVisible();
    await expect(this.page.getByText("What's your turnaround time?")).toBeVisible();
  }

  async clickFAQItem(question: string) {
    await this.page.getByText(question).click();
    await this.page.waitForTimeout(500); // Wait for accordion animation
  }

  async expectFAQAnswer(answer: string) {
    await expect(this.page.getByText(answer)).toBeVisible();
  }

  async testFAQInteractivity() {
    // Test FAQ accordion functionality
    await this.clickFAQItem('What formats do you accept?');
    await this.expectFAQAnswer('We accept WAV, AIFF, and high-quality MP3 files');
    
    await this.clickFAQItem('How many revisions are included?');
    await this.expectFAQAnswer('The number of revisions depends on the package');
  }

  async expectCardHoverEffects() {
    // Test card hover animations (if not reduced motion)
    const mixingCard = this.page.getByText('Mixing').locator('..');
    await mixingCard.hover();
    await this.page.waitForTimeout(300);
    
    // You can add specific hover effect checks here
    // For example, checking for scale transforms or glow effects
  }

  async expectPopularBadge() {
    // Check for "popular" badge on mastering service
    await expect(this.page.getByText('Popular')).toBeVisible();
  }

  async expectResponsiveLayout() {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      // On mobile, cards should stack vertically
      await this.expectPricingCards();
    } else {
      // On desktop, cards should be in a grid
      await this.expectPricingCards();
    }
  }
}
