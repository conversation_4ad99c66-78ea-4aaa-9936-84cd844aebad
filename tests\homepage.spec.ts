import { test, expect } from '@playwright/test';
import { HomePage } from './pages/HomePage';
import { TestHelpers } from './utils/test-helpers';

test.describe('Homepage', () => {
  let homePage: HomePage;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    await homePage.goto();
  });

  test('should have correct title', async ({ page }) => {
    await expect(page).toHaveTitle(/Noize Capital/);
  });

  test('should display main logo', async () => {
    await homePage.expectLogoVisible();
  });

  test('should display tagline', async () => {
    await homePage.expectTaglineVisible();
  });

  test('should display services button', async () => {
    await homePage.expectServicesButtonVisible();
  });

  test('should navigate to services page when clicking services button', async ({ page }) => {
    await homePage.clickServicesButton();
    await expect(page).toHaveURL(/\/services/);
  });

  test('should load without critical console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page, [
      'WebGL',
      'THREE.WebGLRenderer',
      'canvas.getContext'
    ]);
    
    expect(errors).toHaveLength(0);
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await homePage.expectResponsiveLayout();
  });

  test('should be responsive on tablet devices', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await homePage.expectResponsiveLayout();
  });

  test('should be responsive on desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    await homePage.expectResponsiveLayout();
  });

  test('should have proper accessibility features', async ({ page }) => {
    await TestHelpers.checkAccessibility(page);
  });

  test('should have good SEO basics', async ({ page }) => {
    await TestHelpers.checkSEO(page);
  });

  test('should load with reasonable performance', async ({ page }) => {
    await TestHelpers.checkPerformance(page);
  });

  test('should display all main content elements', async () => {
    await homePage.expectPageContent();
  });

  test('should handle logo animations gracefully', async () => {
    await homePage.expectLogoAnimations();
  });

  test('should work with slow network conditions', async ({ page }) => {
    await TestHelpers.simulateSlowNetwork(page);
    await homePage.goto();
    await homePage.expectPageContent();
  });

  test('should maintain layout integrity after window resize', async ({ page }) => {
    // Start with desktop size
    await page.setViewportSize({ width: 1920, height: 1080 });
    await homePage.expectPageContent();
    
    // Resize to mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await TestHelpers.waitForAnimations(page);
    await homePage.expectResponsiveLayout();
    
    // Resize back to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await TestHelpers.waitForAnimations(page);
    await homePage.expectPageContent();
  });

  test('should have working waveform background', async ({ page }) => {
    // Check if waveform background is present (if it exists)
    const waveformElements = page.locator('[class*="waveform"], [class*="background"]');
    const count = await waveformElements.count();
    
    if (count > 0) {
      await expect(waveformElements.first()).toBeVisible();
    }
  });
});
