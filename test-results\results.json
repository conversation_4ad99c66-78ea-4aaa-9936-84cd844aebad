{"config": {"configFile": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\global-setup.ts", "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "demo-working.spec.ts", "file": "demo-working.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Noize Capital - Working Demo Tests", "file": "demo-working.spec.ts", "line": 3, "column": 6, "specs": [{"title": "homepage loads and displays key elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 15334, "errors": [], "stdout": [{"text": "✅ Homepage test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.465Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-94979729fab93327a1d3", "file": "demo-working.spec.ts", "line": 4, "column": 7}, {"title": "navigation to services page works", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 18672, "errors": [], "stdout": [{"text": "✅ Services navigation test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.690Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-50319f134f9a5e3b382d", "file": "demo-working.spec.ts", "line": 22, "column": 7}, {"title": "blog page displays content", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 15079, "errors": [], "stdout": [{"text": "✅ Blog page test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.605Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-18e81ff8deb7e3350073", "file": "demo-working.spec.ts", "line": 40, "column": 7}, {"title": "portfolio page displays projects", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 18678, "errors": [], "stdout": [{"text": "✅ Portfolio page test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.548Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-ea32d646555c54fc7b29", "file": "demo-working.spec.ts", "line": 52, "column": 7}, {"title": "about page displays team information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 16026, "errors": [], "stdout": [{"text": "✅ About page test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.707Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-8f7c13326e32d11b2fa9", "file": "demo-working.spec.ts", "line": 67, "column": 7}, {"title": "responsive design - mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 15055, "errors": [], "stdout": [{"text": "✅ Mobile responsive test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.755Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-2d8fdbbcf770add14802", "file": "demo-working.spec.ts", "line": 82, "column": 7}, {"title": "no critical console errors on homepage", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 14969, "errors": [], "stdout": [{"text": "✅ Console errors test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.715Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-609f95172c6d0fcdfeb8", "file": "demo-working.spec.ts", "line": 95, "column": 7}, {"title": "page performance is reasonable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 13937, "errors": [], "stdout": [{"text": "✅ Performance test passed! Load time: 1995ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:26.764Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-ceedd67d2186e06a67c4", "file": "demo-working.spec.ts", "line": 120, "column": 7}, {"title": "all main pages are accessible", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 11402, "errors": [], "stdout": [{"text": "✅ Homepage accessibility test passed!\n"}, {"text": "✅ Services accessibility test passed!\n"}, {"text": "✅ Portfolio accessibility test passed!\n"}, {"text": "✅ Blog accessibility test passed!\n"}, {"text": "✅ About accessibility test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:41.223Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-442a16a65d63a418fe2b", "file": "demo-working.spec.ts", "line": 134, "column": 7}, {"title": "basic SEO elements are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2610, "errors": [], "stdout": [{"text": "✅ SEO elements test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:40:41.993Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-e9531fe2c49944ae5d7d", "file": "demo-working.spec.ts", "line": 159, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-24T20:40:22.032Z", "duration": 30701.709, "expected": 10, "skipped": 0, "unexpected": 0, "flaky": 0}}