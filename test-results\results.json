{"config": {"configFile": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\global-setup.ts", "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Coding Projects/noize-capital/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Coding Projects/noize-capital/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "demo-working.spec.ts", "file": "demo-working.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Noize Capital - Working Demo Tests", "file": "demo-working.spec.ts", "line": 3, "column": 6, "specs": [{"title": "homepage loads and displays key elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5243, "errors": [], "stdout": [{"text": "✅ Homepage test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.769Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-94979729fab93327a1d3", "file": "demo-working.spec.ts", "line": 4, "column": 7}, {"title": "navigation to services page works", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 11687, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/\\/services/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/\"\u001b[39m\nCall log:\n\u001b[2m  - expect.toHaveURL with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    7 × locator resolved to <html lang=\"en\" class=\"dark\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/\\/services/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/\"\u001b[39m\nCall log:\n\u001b[2m  - expect.toHaveURL with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    7 × locator resolved to <html lang=\"en\" class=\"dark\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\demo-working.spec.ts:30:24", "location": {"file": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\demo-working.spec.ts", "column": 24, "line": 30}, "snippet": "\u001b[0m \u001b[90m 28 |\u001b[39m     \n \u001b[90m 29 |\u001b[39m     \u001b[90m// Check we're on services page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/services/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m     \n \u001b[90m 32 |\u001b[39m     \u001b[90m// Check for pricing information\u001b[39m\n \u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByText(\u001b[32m'$35'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\demo-working.spec.ts", "column": 24, "line": 30}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/\\/services/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/\"\u001b[39m\nCall log:\n\u001b[2m  - expect.toHaveURL with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    7 × locator resolved to <html lang=\"en\" class=\"dark\">…</html>\u001b[22m\n\u001b[2m      - unexpected value \"http://localhost:3000/\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 28 |\u001b[39m     \n \u001b[90m 29 |\u001b[39m     \u001b[90m// Check we're on services page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/services/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m     \n \u001b[90m 32 |\u001b[39m     \u001b[90m// Check for pricing information\u001b[39m\n \u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByText(\u001b[32m'$35'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\demo-working.spec.ts:30:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.760Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\test-results\\demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\test-results\\demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\test-results\\demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Coding Projects\\noize-capital\\tests\\demo-working.spec.ts", "column": 24, "line": 30}}], "status": "unexpected"}], "id": "250978eef8bf7e1ae6af-50319f134f9a5e3b382d", "file": "demo-working.spec.ts", "line": 22, "column": 7}, {"title": "blog page displays content", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 12147, "errors": [], "stdout": [{"text": "✅ Blog page test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.795Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-18e81ff8deb7e3350073", "file": "demo-working.spec.ts", "line": 40, "column": 7}, {"title": "portfolio page displays projects", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 11472, "errors": [], "stdout": [{"text": "✅ Portfolio page test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.812Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-ea32d646555c54fc7b29", "file": "demo-working.spec.ts", "line": 52, "column": 7}, {"title": "about page displays team information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 11364, "errors": [], "stdout": [{"text": "✅ About page test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.811Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-8f7c13326e32d11b2fa9", "file": "demo-working.spec.ts", "line": 67, "column": 7}, {"title": "responsive design - mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 11343, "errors": [], "stdout": [{"text": "✅ Mobile responsive test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.829Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-2d8fdbbcf770add14802", "file": "demo-working.spec.ts", "line": 82, "column": 7}, {"title": "no critical console errors on homepage", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 11078, "errors": [], "stdout": [{"text": "✅ Console errors test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.893Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-609f95172c6d0fcdfeb8", "file": "demo-working.spec.ts", "line": 95, "column": 7}, {"title": "page performance is reasonable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 11246, "errors": [], "stdout": [{"text": "✅ Performance test passed! Load time: 9138ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:20:55.945Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-ceedd67d2186e06a67c4", "file": "demo-working.spec.ts", "line": 120, "column": 7}, {"title": "all main pages are accessible", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 14074, "errors": [], "stdout": [{"text": "✅ Homepage accessibility test passed!\n"}, {"text": "✅ Services accessibility test passed!\n"}, {"text": "✅ Portfolio accessibility test passed!\n"}, {"text": "✅ Blog accessibility test passed!\n"}, {"text": "✅ About accessibility test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:21:01.321Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-442a16a65d63a418fe2b", "file": "demo-working.spec.ts", "line": 134, "column": 7}, {"title": "basic SEO elements are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 1163, "errors": [], "stdout": [{"text": "✅ SEO elements test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-24T20:21:07.333Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "250978eef8bf7e1ae6af-e9531fe2c49944ae5d7d", "file": "demo-working.spec.ts", "line": 159, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-24T20:20:52.982Z", "duration": 22579.477, "expected": 9, "skipped": 0, "unexpected": 1, "flaky": 0}}