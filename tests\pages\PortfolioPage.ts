import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class PortfolioPage extends BasePage {
  readonly pageTitle: Locator;
  readonly filterButtons: Locator;
  readonly projectCards: Locator;
  readonly audioPlayers: Locator;
  readonly callToAction: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.getByRole('heading', { name: /portfolio/i });
    this.filterButtons = page.locator('[data-testid="filter-button"]');
    this.projectCards = page.locator('[data-testid="project-card"]');
    this.audioPlayers = page.locator('audio');
    this.callToAction = page.getByText('Ready to create your next project?');
  }

  async goto() {
    await super.goto('/portfolio');
  }

  async expectPageTitle() {
    await expect(this.pageTitle).toBeVisible();
  }

  async expectProjectCards() {
    // Check for specific projects
    await expect(this.page.getByText('Amadoda')).toBeVisible();
    await expect(this.page.getByText('KilloTronix')).toBeVisible();
  }

  async expectFilterButtons() {
    // Check for category filter buttons
    await expect(this.page.getByText('All')).toBeVisible();
    await expect(this.page.getByText('Mixing')).toBeVisible();
    await expect(this.page.getByText('Mastering')).toBeVisible();
  }

  async clickFilter(filterName: string) {
    await this.page.getByRole('button', { name: filterName }).click();
    await this.page.waitForTimeout(500); // Wait for filter animation
  }

  async expectFilteredResults(category: string) {
    // After clicking a filter, check that only relevant projects are shown
    await this.clickFilter(category);
    
    if (category === 'Mixing') {
      // Expect only mixing projects to be visible
      await expect(this.page.getByText('Mixing')).toBeVisible();
    } else if (category === 'Mastering') {
      // Expect only mastering projects to be visible
      await expect(this.page.getByText('Mastering')).toBeVisible();
    }
  }

  async expectAudioPlayers() {
    // Check for audio players in project cards
    const audioElements = this.page.locator('audio');
    const count = await audioElements.count();
    expect(count).toBeGreaterThan(0);
  }

  async expectSpotifyEmbeds() {
    // Check for Spotify embeds
    const spotifyIframes = this.page.locator('iframe[src*="spotify"]');
    const count = await spotifyIframes.count();
    // Should have at least one Spotify embed
    expect(count).toBeGreaterThan(0);
  }

  async expectProjectDetails() {
    // Check for project information
    await expect(this.page.getByText('2023')).toBeVisible(); // Year
    await expect(this.page.getByText('KilloTronix feat. Nkosana Mkhonza')).toBeVisible(); // Artist
  }

  async expectCallToAction() {
    await expect(this.callToAction).toBeVisible();
    await expect(this.page.getByRole('link', { name: 'Get Started' })).toBeVisible();
  }

  async testProjectCardHover() {
    // Test hover effects on project cards
    const firstCard = this.page.locator('[data-testid="project-card"]').first();
    await firstCard.hover();
    await this.page.waitForTimeout(300);
    
    // You can add specific hover effect checks here
  }

  async expectResponsiveGrid() {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      // On mobile, expect single column layout
      await this.expectProjectCards();
    } else {
      // On desktop, expect multi-column grid
      await this.expectProjectCards();
    }
  }

  async testFilterFunctionality() {
    // Test the complete filter workflow
    await this.expectFilterButtons();
    
    // Test "All" filter
    await this.clickFilter('All');
    await this.expectProjectCards();
    
    // Test "Mixing" filter
    await this.clickFilter('Mixing');
    await this.page.waitForTimeout(500);
    
    // Test "Mastering" filter
    await this.clickFilter('Mastering');
    await this.page.waitForTimeout(500);
    
    // Return to "All"
    await this.clickFilter('All');
  }
}
