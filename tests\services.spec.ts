import { test, expect } from '@playwright/test';
import { ServicesPage } from './pages/ServicesPage';
import { TestHelpers } from './utils/test-helpers';

test.describe('Services Page', () => {
  let servicesPage: ServicesPage;

  test.beforeEach(async ({ page }) => {
    servicesPage = new ServicesPage(page);
    await servicesPage.goto();
  });

  test('should display page title', async () => {
    await servicesPage.expectPageTitle();
  });

  test('should display all pricing cards', async () => {
    await servicesPage.expectPricingCards();
  });

  test('should display correct pricing information', async () => {
    await servicesPage.expectPricingInformation();
  });

  test('should display service features', async () => {
    await servicesPage.expectServiceFeatures();
  });

  test('should display popular badge on mastering service', async () => {
    await servicesPage.expectPopularBadge();
  });

  test('should display FAQ section', async () => {
    await servicesPage.expectFAQSection();
  });

  test('should have interactive FAQ accordions', async () => {
    await servicesPage.testFAQInteractivity();
  });

  test('should have card hover effects', async ({ page }) => {
    await servicesPage.expectCardHoverEffects();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await servicesPage.expectResponsiveLayout();
  });

  test('should be responsive on tablet', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await servicesPage.expectResponsiveLayout();
  });

  test('should be responsive on desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    await servicesPage.expectResponsiveLayout();
  });

  test('should load without console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page);
    expect(errors).toHaveLength(0);
  });

  test('should have proper accessibility features', async ({ page }) => {
    await TestHelpers.checkAccessibility(page);
  });

  test('should have good SEO basics', async ({ page }) => {
    await TestHelpers.checkSEO(page);
  });

  test('should display service emojis', async ({ page }) => {
    await expect(page.getByText('🎛️')).toBeVisible(); // Mixing
    await expect(page.getByText('🎚️')).toBeVisible(); // Mastering
    await expect(page.getByText('💽')).toBeVisible(); // Full Package
  });

  test('should show delivery timeframes', async ({ page }) => {
    await expect(page.getByText('Delivery in 7 days')).toBeVisible(); // Mixing
    await expect(page.getByText('Delivery in 5 days')).toBeVisible(); // Mastering
    await expect(page.getByText('Delivery in 10 days')).toBeVisible(); // Full Package
  });

  test('should show revision counts', async ({ page }) => {
    await expect(page.getByText('2 revisions')).toBeVisible(); // Mixing
    await expect(page.getByText('3 revisions')).toBeVisible(); // Mastering
    await expect(page.getByText('5 revisions')).toBeVisible(); // Full Package
  });

  test('FAQ should expand and collapse correctly', async ({ page }) => {
    // Test first FAQ item
    const firstQuestion = 'What formats do you accept?';
    await servicesPage.clickFAQItem(firstQuestion);
    await servicesPage.expectFAQAnswer('We accept WAV, AIFF, and high-quality MP3 files');
    
    // Click again to collapse
    await servicesPage.clickFAQItem(firstQuestion);
    await page.waitForTimeout(500);
    
    // Test second FAQ item
    const secondQuestion = 'How many revisions are included?';
    await servicesPage.clickFAQItem(secondQuestion);
    await servicesPage.expectFAQAnswer('The number of revisions depends on the package');
  });

  test('should handle multiple FAQ items open simultaneously', async ({ page }) => {
    // Open multiple FAQ items
    await servicesPage.clickFAQItem('What formats do you accept?');
    await servicesPage.clickFAQItem('How many revisions are included?');
    await servicesPage.clickFAQItem("What's your turnaround time?");
    
    // All answers should be visible
    await servicesPage.expectFAQAnswer('We accept WAV, AIFF, and high-quality MP3 files');
    await servicesPage.expectFAQAnswer('The number of revisions depends on the package');
    await servicesPage.expectFAQAnswer('Turnaround times vary by service');
  });

  test('should maintain layout on window resize', async ({ page }) => {
    // Start with desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await servicesPage.expectPricingCards();
    
    // Resize to mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await TestHelpers.waitForAnimations(page);
    await servicesPage.expectResponsiveLayout();
    
    // Resize back to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await TestHelpers.waitForAnimations(page);
    await servicesPage.expectPricingCards();
  });

  test('should work with slow network', async ({ page }) => {
    await TestHelpers.simulateSlowNetwork(page);
    await servicesPage.goto();
    await servicesPage.expectPricingCards();
  });

  test('should have working contact/CTA buttons', async ({ page }) => {
    // Look for contact or get started buttons
    const ctaButtons = page.locator('a[href*="contact"], button:has-text("Get Started"), a:has-text("Get Started")');
    const count = await ctaButtons.count();
    
    if (count > 0) {
      await expect(ctaButtons.first()).toBeVisible();
      await ctaButtons.first().click();
      await TestHelpers.waitForNetworkIdle(page);
    }
  });
});
