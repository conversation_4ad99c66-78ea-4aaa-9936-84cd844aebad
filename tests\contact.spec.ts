import { test, expect } from '@playwright/test';
import { ContactPage } from './pages/ContactPage';
import { TestHelpers } from './utils/test-helpers';

test.describe('Contact Page', () => {
  let contactPage: ContactPage;

  test.beforeEach(async ({ page }) => {
    contactPage = new ContactPage(page);
    await contactPage.goto();
  });

  test('should display page title', async () => {
    await contactPage.expectPageTitle();
  });

  test('should display contact form', async () => {
    await contactPage.expectContactForm();
  });

  test('should validate required fields', async () => {
    await contactPage.testFormValidation();
  });

  test('should validate email format', async () => {
    await contactPage.testInvalidEmail();
  });

  test('should submit valid form', async () => {
    await contactPage.testValidFormSubmission();
  });

  test('should display contact information', async () => {
    await contactPage.expectContactInformation();
  });

  test('should display social links', async () => {
    await contactPage.expectSocialLinks();
  });

  test('should be responsive', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await contactPage.expectResponsiveLayout();
  });

  test('should load without console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page);
    expect(errors).toHaveLength(0);
  });

  test('should have proper accessibility features', async ({ page }) => {
    await TestHelpers.checkAccessibility(page);
  });

  test('should handle form submission with test data', async ({ page }) => {
    const testData = TestHelpers.generateTestData();
    
    await contactPage.fillContactForm(
      testData.user.name,
      testData.user.email,
      testData.user.message
    );
    
    await contactPage.submitForm();
    
    // Wait for response (success or error)
    await page.waitForTimeout(2000);
  });

  test('should handle long messages', async ({ page }) => {
    const testData = TestHelpers.generateTestData();
    
    await contactPage.fillContactForm(
      testData.user.name,
      testData.user.email,
      testData.longMessage
    );
    
    // Should accept long messages
    const messageValue = await contactPage.messageInput.inputValue();
    expect(messageValue.length).toBeGreaterThan(500);
  });
});
