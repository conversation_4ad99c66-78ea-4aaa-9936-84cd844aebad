import { test, expect } from '@playwright/test';

test.describe('Noize Capital - Working Demo Tests', () => {
  test('homepage loads and displays key elements', async ({ page }) => {
    await page.goto('http://localhost:3000');
    
    // Check title
    await expect(page).toHaveTitle(/Noize Capital/);
    
    // Check main logo is visible
    await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
    
    // Check tagline
    await expect(page.getByText('Where we make your ideas audible')).toBeVisible();
    
    // Check services button
    await expect(page.getByRole('link', { name: 'Our Services' })).toBeVisible();
    
    console.log('✅ Homepage test passed!');
  });

  test('navigation to services page works', async ({ page }) => {
    await page.goto('http://localhost:3000');
    
    // Click on Services in navigation
    await page.getByRole('link', { name: 'Services' }).first().click();
    await page.waitForLoadState('networkidle');
    
    // Check we're on services page
    await expect(page).toHaveURL(/\/services/);
    
    // Check for pricing information
    await expect(page.getByText('$35')).toBeVisible();
    await expect(page.getByText('$20')).toBeVisible();
    await expect(page.getByText('$50')).toBeVisible();
    
    console.log('✅ Services navigation test passed!');
  });

  test('blog page displays content', async ({ page }) => {
    await page.goto('http://localhost:3000/blog');
    
    // Check page loads
    await expect(page.getByText('Insights, tutorials, and guides')).toBeVisible();
    
    // Check for specific blog post (using partial text to avoid strict mode)
    await expect(page.getByText('AI Mastering vs $500 Professional Mastering')).toBeVisible();
    
    console.log('✅ Blog page test passed!');
  });

  test('portfolio page displays projects', async ({ page }) => {
    await page.goto('http://localhost:3000/portfolio');
    
    // Check page title
    await expect(page.getByText('Our Portfolio')).toBeVisible();
    
    // Check for specific project
    await expect(page.getByText('Amadoda')).toBeVisible();
    
    // Check for filter button (using role to be more specific)
    await expect(page.getByRole('button', { name: 'All' })).toBeVisible();
    
    console.log('✅ Portfolio page test passed!');
  });

  test('about page displays team information', async ({ page }) => {
    await page.goto('http://localhost:3000/about');
    
    // Check page title
    await expect(page.getByText('About Us')).toBeVisible();
    
    // Check for company description
    await expect(page.getByText('Passionate about sound, dedicated to your music')).toBeVisible();
    
    // Check for founder info (using more specific selector)
    await expect(page.getByText('Founder & Lead Engineer')).toBeVisible();
    
    console.log('✅ About page test passed!');
  });

  test('responsive design - mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('http://localhost:3000');
    
    // Check that main content is still visible on mobile
    await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
    await expect(page.getByText('Where we make your ideas audible')).toBeVisible();
    
    console.log('✅ Mobile responsive test passed!');
  });

  test('no critical console errors on homepage', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        // Filter out expected WebGL errors in headless mode
        if (!text.includes('WebGL') && 
            !text.includes('THREE.WebGLRenderer') && 
            !text.includes('canvas.getContext') &&
            !text.includes('WebGL context')) {
          errors.push(text);
        }
      }
    });

    await page.goto('http://localhost:3000');
    await page.waitForTimeout(3000); // Wait for any async operations
    
    // Should have no critical errors
    expect(errors).toHaveLength(0);
    
    console.log('✅ Console errors test passed!');
  });

  test('page performance is reasonable', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 15 seconds (generous for CI/slow networks)
    expect(loadTime).toBeLessThan(15000);
    
    console.log(`✅ Performance test passed! Load time: ${loadTime}ms`);
  });

  test('all main pages are accessible', async ({ page }) => {
    const pages = [
      { path: '/', name: 'Homepage' },
      { path: '/services', name: 'Services' },
      { path: '/portfolio', name: 'Portfolio' },
      { path: '/blog', name: 'Blog' },
      { path: '/about', name: 'About' },
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.path}`);
      await page.waitForLoadState('networkidle');
      
      // Check that page loads (has title)
      await expect(page).toHaveTitle(/Noize Capital/);
      
      // Check that page has content (body is not empty)
      const bodyText = await page.textContent('body');
      expect(bodyText).toBeTruthy();
      expect(bodyText!.length).toBeGreaterThan(100);
      
      console.log(`✅ ${pageInfo.name} accessibility test passed!`);
    }
  });

  test('basic SEO elements are present', async ({ page }) => {
    await page.goto('http://localhost:3000');
    
    // Check title
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(10);
    
    // Check meta description exists
    const metaDescription = await page.locator('meta[name="description"]').getAttribute('content');
    expect(metaDescription).toBeTruthy();
    
    console.log('✅ SEO elements test passed!');
  });
});
