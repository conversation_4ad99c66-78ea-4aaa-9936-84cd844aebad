import { test, expect } from '@playwright/test';
import { PortfolioPage } from './pages/PortfolioPage';
import { TestHelpers } from './utils/test-helpers';

test.describe('Portfolio Page', () => {
  let portfolioPage: PortfolioPage;

  test.beforeEach(async ({ page }) => {
    portfolioPage = new PortfolioPage(page);
    await portfolioPage.goto();
  });

  test('should display page title', async () => {
    await portfolioPage.expectPageTitle();
  });

  test('should display project cards', async () => {
    await portfolioPage.expectProjectCards();
  });

  test('should display filter buttons', async () => {
    await portfolioPage.expectFilterButtons();
  });

  test('should display project details', async () => {
    await portfolioPage.expectProjectDetails();
  });

  test('should display call to action', async () => {
    await portfolioPage.expectCallToAction();
  });

  test('should have working filter functionality', async () => {
    await portfolioPage.testFilterFunctionality();
  });

  test('should filter projects by category', async () => {
    await portfolioPage.expectFilteredResults('Mixing');
    await portfolioPage.expectFilteredResults('Mastering');
  });

  test('should display audio players', async () => {
    await portfolioPage.expectAudioPlayers();
  });

  test('should display Spotify embeds', async () => {
    await portfolioPage.expectSpotifyEmbeds();
  });

  test('should have project card hover effects', async () => {
    await portfolioPage.testProjectCardHover();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await portfolioPage.expectResponsiveGrid();
  });

  test('should be responsive on tablet', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await portfolioPage.expectResponsiveGrid();
  });

  test('should be responsive on desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    await portfolioPage.expectResponsiveGrid();
  });

  test('should load without console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page);
    expect(errors).toHaveLength(0);
  });

  test('should have proper accessibility features', async ({ page }) => {
    await TestHelpers.checkAccessibility(page);
  });

  test('should have good SEO basics', async ({ page }) => {
    await TestHelpers.checkSEO(page);
  });

  test('should display project images', async ({ page }) => {
    const images = page.locator('img[alt*="by"]'); // Project images with alt text
    const count = await images.count();
    expect(count).toBeGreaterThan(0);
    
    // Check that images load properly
    for (let i = 0; i < Math.min(count, 3); i++) {
      await expect(images.nth(i)).toBeVisible();
    }
  });

  test('should show project categories', async ({ page }) => {
    await expect(page.getByText('Mixing')).toBeVisible();
    await expect(page.getByText('Mastering')).toBeVisible();
  });

  test('should show project years', async ({ page }) => {
    await expect(page.getByText('2023')).toBeVisible();
  });

  test('should handle filter state correctly', async ({ page }) => {
    // Click Mixing filter
    await portfolioPage.clickFilter('Mixing');
    
    // Check that filter button is active
    const mixingButton = page.getByRole('button', { name: 'Mixing' });
    const classes = await mixingButton.getAttribute('class');
    expect(classes).toContain('bg-orange-500'); // Active filter class
    
    // Reset to All
    await portfolioPage.clickFilter('All');
    const allButton = page.getByRole('button', { name: 'All' });
    const allClasses = await allButton.getAttribute('class');
    expect(allClasses).toContain('bg-orange-500');
  });

  test('should maintain filter state during interactions', async ({ page }) => {
    // Apply a filter
    await portfolioPage.clickFilter('Mastering');
    
    // Hover over a project card
    await portfolioPage.testProjectCardHover();
    
    // Filter should still be active
    const masteringButton = page.getByRole('button', { name: 'Mastering' });
    const classes = await masteringButton.getAttribute('class');
    expect(classes).toContain('bg-orange-500');
  });

  test('should handle audio player interactions', async ({ page }) => {
    // Find audio elements
    const audioElements = page.locator('audio');
    const count = await audioElements.count();
    
    if (count > 0) {
      const firstAudio = audioElements.first();
      
      // Check if audio element has controls
      const hasControls = await firstAudio.getAttribute('controls');
      expect(hasControls).toBeTruthy();
      
      // Check if audio has a source
      const src = await firstAudio.getAttribute('src');
      expect(src).toBeTruthy();
    }
  });

  test('should work with slow network', async ({ page }) => {
    await TestHelpers.simulateSlowNetwork(page);
    await portfolioPage.goto();
    await portfolioPage.expectProjectCards();
  });

  test('should handle window resize gracefully', async ({ page }) => {
    // Start with desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await portfolioPage.expectResponsiveGrid();
    
    // Resize to mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await TestHelpers.waitForAnimations(page);
    await portfolioPage.expectResponsiveGrid();
    
    // Resize back to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await TestHelpers.waitForAnimations(page);
    await portfolioPage.expectResponsiveGrid();
  });

  test('should have working CTA button', async ({ page }) => {
    await portfolioPage.expectCallToAction();
    
    const ctaButton = page.getByRole('link', { name: 'Get Started' });
    await expect(ctaButton).toBeVisible();
    
    await ctaButton.click();
    await TestHelpers.waitForNetworkIdle(page);
    
    // Should navigate to contact or services page
    const url = page.url();
    expect(url).toMatch(/\/(contact|services)/);
  });
});
