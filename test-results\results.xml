<testsuites id="" name="" tests="10" failures="1" skipped="0" errors="0" time="22.579477">
<testsuite name="demo-working.spec.ts" timestamp="2025-05-24T20:20:54.697Z" hostname="chromium" tests="10" failures="1" skipped="0" time="100.817" errors="0">
<testcase name="Noize Capital - Working Demo Tests › homepage loads and displays key elements" classname="demo-working.spec.ts" time="5.243">
<system-out>
<![CDATA[✅ Homepage test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › navigation to services page works" classname="demo-working.spec.ts" time="11.687">
<failure message="demo-working.spec.ts:22:7 navigation to services page works" type="FAILURE">
<![CDATA[  [chromium] › demo-working.spec.ts:22:7 › Noize Capital - Working Demo Tests › navigation to services page works 

    Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)

    Locator: locator(':root')
    Expected pattern: /\/services/
    Received string:  "http://localhost:3000/"
    Call log:
      - expect.toHaveURL with timeout 5000ms
      - waiting for locator(':root')
        7 × locator resolved to <html lang="en" class="dark">…</html>
          - unexpected value "http://localhost:3000/"


      28 |     
      29 |     // Check we're on services page
    > 30 |     await expect(page).toHaveURL(/\/services/);
         |                        ^
      31 |     
      32 |     // Check for pricing information
      33 |     await expect(page.getByText('$35')).toBeVisible();
        at C:\Users\<USER>\Coding Projects\noize-capital\tests\demo-working.spec.ts:30:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\test-failed-1.png]]

[[ATTACHMENT|demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\video.webm]]

[[ATTACHMENT|demo-working-Noize-Capital-7ec43-tion-to-services-page-works-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › blog page displays content" classname="demo-working.spec.ts" time="12.147">
<system-out>
<![CDATA[✅ Blog page test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › portfolio page displays projects" classname="demo-working.spec.ts" time="11.472">
<system-out>
<![CDATA[✅ Portfolio page test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › about page displays team information" classname="demo-working.spec.ts" time="11.364">
<system-out>
<![CDATA[✅ About page test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › responsive design - mobile viewport" classname="demo-working.spec.ts" time="11.343">
<system-out>
<![CDATA[✅ Mobile responsive test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › no critical console errors on homepage" classname="demo-working.spec.ts" time="11.078">
<system-out>
<![CDATA[✅ Console errors test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › page performance is reasonable" classname="demo-working.spec.ts" time="11.246">
<system-out>
<![CDATA[✅ Performance test passed! Load time: 9138ms
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › all main pages are accessible" classname="demo-working.spec.ts" time="14.074">
<system-out>
<![CDATA[✅ Homepage accessibility test passed!
✅ Services accessibility test passed!
✅ Portfolio accessibility test passed!
✅ Blog accessibility test passed!
✅ About accessibility test passed!
]]>
</system-out>
</testcase>
<testcase name="Noize Capital - Working Demo Tests › basic SEO elements are present" classname="demo-working.spec.ts" time="1.163">
<system-out>
<![CDATA[✅ SEO elements test passed!
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>