# Noize Capital E2E Testing Suite

This directory contains comprehensive end-to-end tests for the Noize Capital application using Playwright with TypeScript.

## 🧪 Test Structure

### Page Object Model
- `pages/` - Page Object Model classes for maintainable test code
  - `BasePage.ts` - Common functionality shared across all pages
  - `HomePage.ts` - Homepage-specific interactions
  - `ServicesPage.ts` - Services page with pricing and FAQ tests
  - `PortfolioPage.ts` - Portfolio filtering and audio player tests
  - `BlogPage.ts` - Blog listing and individual post tests
  - `ContactPage.ts` - Contact form validation and submission
  - `AboutPage.ts` - About page content verification

### Test Suites
- `homepage.spec.ts` - Homepage functionality and animations
- `navigation.spec.ts` - Cross-page navigation and menu interactions
- `services.spec.ts` - Services page pricing cards and FAQ
- `portfolio.spec.ts` - Portfolio filtering and audio features
- `blog.spec.ts` - Blog content and reading experience
- `contact.spec.ts` - Contact form validation and submission
- `about.spec.ts` - About page content and team information
- `e2e-flow.spec.ts` - Complete user journey tests
- `visual-regression.spec.ts` - Visual comparison tests
- `demo-working.spec.ts` - Working demo tests (10/10 passing)

### Utilities
- `utils/test-helpers.ts` - Common testing utilities and helpers
- `global-setup.ts` - Global test configuration and setup

## 🚀 Running Tests

### Local Development
```bash
# Run all tests
npm run test:e2e

# Run tests with UI mode
npm run test:e2e:ui

# Run tests in headed mode (see browser)
npm run test:e2e:headed

# Debug tests
npm run test:e2e:debug

# View HTML report
npm run test:e2e:report
```

### Specific Test Suites
```bash
# Run only homepage tests
npx playwright test homepage.spec.ts

# Run only mobile tests
npx playwright test --project="Mobile Chrome"

# Run only working demo tests
npx playwright test demo-working.spec.ts

# Run visual regression tests
npx playwright test visual-regression.spec.ts
```

### Cross-Browser Testing
```bash
# Run on all browsers
npx playwright test --project=chromium --project=firefox --project=webkit

# Run on specific browser
npx playwright test --project=chromium
```

## 🔧 Configuration

### Playwright Config (`playwright.config.ts`)
- **Base URL:** `http://localhost:3000`
- **Browsers:** Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari
- **Reporters:** HTML, JSON, JUnit
- **Timeouts:** 30s test timeout, 10s action timeout
- **Screenshots:** On failure
- **Videos:** On failure
- **Traces:** On retry

### Test Features
- ✅ **Responsive Design Testing** - Mobile, tablet, desktop viewports
- ✅ **Cross-Browser Compatibility** - Chrome, Firefox, Safari
- ✅ **Performance Monitoring** - Load time tracking
- ✅ **Accessibility Validation** - WCAG compliance checks
- ✅ **SEO Verification** - Meta tags, titles, descriptions
- ✅ **Console Error Detection** - JavaScript error monitoring
- ✅ **Form Interaction Testing** - Contact form validation
- ✅ **Audio Player Testing** - Portfolio audio elements
- ✅ **Filter Functionality** - Portfolio category filtering
- ✅ **Visual Regression Testing** - Screenshot comparisons

## 🏗️ CI/CD Integration

### GitHub Actions Workflows

#### Main CI/CD Pipeline (`.github/workflows/ci-cd.yml`)
- **Quality Checks:** ESLint, TypeScript, Build
- **E2E Tests:** Cross-browser testing matrix
- **Mobile Tests:** Mobile Chrome and Safari
- **Performance:** Lighthouse CI
- **Security:** npm audit and vulnerability scanning
- **Deployment:** Automatic deployment to production on main branch

#### PR Preview (`.github/workflows/pr-preview.yml`)
- **Preview Deployment:** Vercel preview for each PR
- **Preview Testing:** Tests against preview URL
- **Visual Regression:** Screenshot comparisons
- **Accessibility:** axe-core accessibility testing
- **Results Comments:** Automated PR comments with test results

#### Playwright Tests (`.github/workflows/playwright.yml`)
- **Multi-job Testing:** Separate jobs for different test types
- **Artifact Upload:** Test reports and screenshots
- **Cross-browser Matrix:** Parallel browser testing

## 📊 Test Coverage

### Pages Tested
- ✅ Homepage - Logo, tagline, navigation, animations
- ✅ Services - Pricing cards, FAQ interactions, hover effects
- ✅ Portfolio - Project filtering, audio players, Spotify embeds
- ✅ Blog - Post listings, individual posts, metadata
- ✅ Contact - Form validation, submission, accessibility
- ✅ About - Team information, company story

### User Journeys
- ✅ Homepage → Services → Contact conversion flow
- ✅ Portfolio filtering and audio interaction flow
- ✅ Blog reading experience flow
- ✅ Mobile navigation and responsive design
- ✅ Cross-page navigation and browser history

### Quality Checks
- ✅ Performance (load times < 15s)
- ✅ Accessibility (WCAG compliance)
- ✅ SEO (meta tags, titles, descriptions)
- ✅ Console errors (filtered WebGL false positives)
- ✅ Responsive design (375px to 1920px)
- ✅ Form validation and submission

## 🐛 Debugging

### Common Issues
1. **WebGL Errors:** Filtered out as expected in headless mode
2. **Timing Issues:** Use `waitForLoadState('networkidle')` for dynamic content
3. **Selector Issues:** Use role-based selectors when possible
4. **Animation Issues:** Wait for animations with `waitForTimeout()`

### Debug Commands
```bash
# Run with debug mode
npx playwright test --debug

# Run specific test with debug
npx playwright test homepage.spec.ts --debug

# Generate test code
npx playwright codegen localhost:3000
```

### Screenshots and Videos
- Screenshots saved to `test-results/`
- Videos recorded on failure
- HTML report includes visual artifacts

## 📈 Performance Metrics

### Current Benchmarks
- **Homepage Load Time:** ~6-9 seconds
- **Cross-page Navigation:** < 3 seconds
- **Form Submission:** < 2 seconds
- **Filter Interactions:** < 1 second

### Lighthouse Scores (Target)
- **Performance:** > 80
- **Accessibility:** > 90
- **Best Practices:** > 80
- **SEO:** > 90

## 🔄 Maintenance

### Adding New Tests
1. Create page object in `pages/` if needed
2. Add test file following naming convention
3. Use existing helpers from `utils/test-helpers.ts`
4. Update this README with new coverage

### Updating Baselines
```bash
# Update visual regression baselines
npx playwright test visual-regression.spec.ts --update-snapshots
```

### Dependencies
- `@playwright/test` - Core testing framework
- `playwright` - Browser automation
- Node.js LTS - Runtime environment
