name: Playwright Tests (Simple)

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium
    
    - name: Build Next.js application
      run: npm run build
    
    - name: Run Playwright tests (Chromium only)
      run: npx playwright test --project=chromium
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
    
    - name: Comment PR with test results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          let testResults = 'Tests completed! Check the artifacts for detailed results.';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🧪 Playwright Test Results\n\n${testResults}\n\n[View detailed report in artifacts](${context.payload.pull_request.html_url}/checks)`
          });
